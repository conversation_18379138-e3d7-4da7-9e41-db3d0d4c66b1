[gd_scene load_steps=5 format=3 uid="uid://audio_settings_scene"]

[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="1_background"]
[ext_resource type="Script" path="res://scripts/AudioSettings.gd" id="2_audio_settings_script"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_panel"]
[ext_resource type="Texture2D" uid="uid://dv7iqf557av33" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Button/Button normal.png" id="4_button"]
[ext_resource type="Texture2D" uid="uid://8sprbwyw5ri8" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Middle Message/Button/Button hover.png" id="5_button_hover"]

[node name="AudioSettings" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("2_audio_settings_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("1_background")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_panel")
patch_margin_left = 50
patch_margin_top = 50
patch_margin_right = 50
patch_margin_bottom = 50

[node name="ContentContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0

[node name="TitleLabel" type="Label" parent="MainPanel/ContentContainer"]
layout_mode = 2
text = "AUDIO NASTAVENIA"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="MusicContainer" type="HBoxContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="MusicLabel" type="Label" parent="MainPanel/ContentContainer/MusicContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Hudba:"

[node name="MusicSlider" type="HSlider" parent="MainPanel/ContentContainer/MusicContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.7

[node name="MusicValue" type="Label" parent="MainPanel/ContentContainer/MusicContainer"]
layout_mode = 2
text = "70%"

[node name="SFXContainer" type="HBoxContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="SFXLabel" type="Label" parent="MainPanel/ContentContainer/SFXContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Zvukové efekty:"

[node name="SFXSlider" type="HSlider" parent="MainPanel/ContentContainer/SFXContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.8

[node name="SFXValue" type="Label" parent="MainPanel/ContentContainer/SFXContainer"]
layout_mode = 2
text = "80%"

[node name="UIContainer" type="HBoxContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="UILabel" type="Label" parent="MainPanel/ContentContainer/UIContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "UI zvuky:"

[node name="UISlider" type="HSlider" parent="MainPanel/ContentContainer/UIContainer"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.6

[node name="UIValue" type="Label" parent="MainPanel/ContentContainer/UIContainer"]
layout_mode = 2
text = "60%"

[node name="HSeparator2" type="HSeparator" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2

[node name="TestButton" type="TextureButton" parent="MainPanel/ContentContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(120, 40)
texture_normal = ExtResource("4_button")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="TestLabel" type="Label" parent="MainPanel/ContentContainer/ButtonContainer/TestButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "🎵 Test všetkých trackov"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackButton" type="TextureButton" parent="MainPanel/ContentContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(120, 40)
texture_normal = ExtResource("4_button")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="BackLabel" type="Label" parent="MainPanel/ContentContainer/ButtonContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Späť"
horizontal_alignment = 1
vertical_alignment = 1
