[gd_scene load_steps=9 format=3 uid="uid://px7vn7qkqxqxq"]

[ext_resource type="Script" path="res://scripts/ThreeSistersPuzzle.gd" id="1_sisters"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="2_panel"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="3_theme"]
[ext_resource type="Texture2D" path="res://assets/Portrety/Maria.png" id="4_maria"]
[ext_resource type="Texture2D" path="res://assets/Portrety/Anna.png" id="5_anna"]
[ext_resource type="Texture2D" path="res://assets/Portrety/Isabelle.png" id="6_isabelle"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Normal.png" id="7_button_normal"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Hover.png" id="8_button_hover"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 24
font_color = Color(0.9, 0.8, 0.6, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[sub_resource type="LabelSettings" id="LabelSettings_clue"]
font_size = 18
font_color = Color(0.8, 0.6, 0.9, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[node name="ThreeSistersPuzzle" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("3_theme")
script = ExtResource("1_sisters")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="PuzzlePanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -320.0
offset_top = -450.0
offset_right = 320.0
offset_bottom = 450.0
texture = ExtResource("2_panel")
patch_margin_left = 16
patch_margin_top = 16
patch_margin_right = 16
patch_margin_bottom = 16

[node name="VBoxContainer" type="VBoxContainer" parent="PuzzlePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
theme_override_constants/separation = 12

[node name="TitleLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Tri sestry"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="DescriptionLabel" type="RichTextLabel" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[center]Na stene sú tri portréty sestier s menovkami.[/center]

[b]Úloha:[/b] Kliknite na portrét sestry, ktorá hovorí pravdu."
fit_content = true

[node name="Spacer2" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="StatementsLabel" type="RichTextLabel" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[center][b]Výroky:[/b]
• [color=lightblue]Mária:[/color] \"Anna je nevinná.\"
• [color=lightgreen]Anna:[/color] \"Isabelle zradila rod.\"
• [color=lightcoral]Isabelle:[/color] \"Mária klame.\"[/center]"
fit_content = true

[node name="Spacer3" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="ClueLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "\"Len jedna hovorí pravdu. Tá, čo klame, je vinná.\""
label_settings = SubResource("LabelSettings_clue")
horizontal_alignment = 1

[node name="Spacer4" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PortraitsContainer" type="HBoxContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="MariaButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/PortraitsContainer"]
custom_minimum_size = Vector2(180, 220)
layout_mode = 2
texture_normal = ExtResource("4_maria")
ignore_texture_size = true
stretch_mode = 1

[node name="MariaLabel" type="Label" parent="PuzzlePanel/VBoxContainer/PortraitsContainer/MariaButton"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -30.0
offset_top = -25.0
offset_right = 30.0
text = "Mária"
horizontal_alignment = 1

[node name="Spacer" type="Control" parent="PuzzlePanel/VBoxContainer/PortraitsContainer"]
custom_minimum_size = Vector2(20, 0)
layout_mode = 2

[node name="AnnaButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/PortraitsContainer"]
custom_minimum_size = Vector2(180, 220)
layout_mode = 2
texture_normal = ExtResource("5_anna")
ignore_texture_size = true
stretch_mode = 1

[node name="AnnaLabel" type="Label" parent="PuzzlePanel/VBoxContainer/PortraitsContainer/AnnaButton"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -30.0
offset_top = -25.0
offset_right = 30.0
text = "Anna"
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="PuzzlePanel/VBoxContainer/PortraitsContainer"]
custom_minimum_size = Vector2(20, 0)
layout_mode = 2

[node name="IsabelleButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/PortraitsContainer"]
custom_minimum_size = Vector2(180, 220)
layout_mode = 2
texture_normal = ExtResource("6_isabelle")
ignore_texture_size = true
stretch_mode = 1

[node name="IsabelleLabel" type="Label" parent="PuzzlePanel/VBoxContainer/PortraitsContainer/IsabelleButton"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -30.0
offset_top = -25.0
offset_right = 30.0
text = "Isabelle"
horizontal_alignment = 1

[node name="Spacer5" type="Control" parent="PuzzlePanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2

[node name="HintButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(150, 50)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("7_button_normal")
texture_hover = ExtResource("8_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="HintLabel" type="Label" parent="PuzzlePanel/VBoxContainer/ButtonContainer/HintButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Nápoveda"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CloseButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(150, 50)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("7_button_normal")
texture_hover = ExtResource("8_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="CloseLabel" type="Label" parent="PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Zavrieť"
horizontal_alignment = 1
vertical_alignment = 1
