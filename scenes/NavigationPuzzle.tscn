[gd_scene load_steps=4 format=3 uid="uid://ix5vn4qkqxqxq"]

[ext_resource type="Script" path="res://scripts/NavigationPuzzle.gd" id="1_navigation"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="2_panel"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="3_theme"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Normal.png" id="4_button_normal"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Hover.png" id="5_button_hover"]

[node name="NavigationPuzzle" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("3_theme")
script = ExtResource("1_navigation")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="PuzzlePanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -320.0
offset_top = -450.0
offset_right = 320.0
offset_bottom = 450.0
texture = ExtResource("2_panel")
patch_margin_left = 25
patch_margin_top = 25
patch_margin_right = 25
patch_margin_bottom = 25

[node name="VBoxContainer" type="VBoxContainer" parent="PuzzlePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="TitleLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Cesta lesom"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="DescriptionLabel" type="RichTextLabel" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 150)
bbcode_enabled = true
text = "[center]Cesta sa rozdeľuje na štyri smery.[/center]

[b]Van Helsingova poznámka:[/b]
'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'

Vyberte správny smer:"
fit_content = true

[node name="StepLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Stojíte na križovatke lesných ciest. Kam pôjdete?"
horizontal_alignment = 1
autowrap_mode = 3

[node name="Spacer2" type="Control" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="DirectionContainer" type="GridContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
columns = 3

[node name="EmptySpace1" type="Control" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2

[node name="NorthButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 80)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="NorthLabel" type="Label" parent="PuzzlePanel/VBoxContainer/DirectionContainer/NorthButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "↑
SEVER"
horizontal_alignment = 1
vertical_alignment = 1

[node name="EmptySpace2" type="Control" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2

[node name="WestButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 80)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="WestLabel" type="Label" parent="PuzzlePanel/VBoxContainer/DirectionContainer/WestButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "←
ZÁPAD"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CenterLabel" type="Label" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 80)
text = "🧭"
horizontal_alignment = 1
vertical_alignment = 1

[node name="EastButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 80)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="EastLabel" type="Label" parent="PuzzlePanel/VBoxContainer/DirectionContainer/EastButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "→
VÝCHOD"
horizontal_alignment = 1
vertical_alignment = 1

[node name="EmptySpace3" type="Control" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2

[node name="SouthButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 80)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="SouthLabel" type="Label" parent="PuzzlePanel/VBoxContainer/DirectionContainer/SouthButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "↓
JUH"
horizontal_alignment = 1
vertical_alignment = 1

[node name="EmptySpace4" type="Control" parent="PuzzlePanel/VBoxContainer/DirectionContainer"]
layout_mode = 2

[node name="Spacer3" type="Control" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ProgressLabel" type="Label" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
text = "Kroky: ?"
horizontal_alignment = 1

[node name="Spacer4" type="Control" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ButtonContainer" type="HBoxContainer" parent="PuzzlePanel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="HintButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="HintLabel" type="Label" parent="PuzzlePanel/VBoxContainer/ButtonContainer/HintButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Nápoveda"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer_Button1" type="Control" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(15, 0)

[node name="ResetButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="ResetLabel" type="Label" parent="PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Reset"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer_Button2" type="Control" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(15, 0)

[node name="CloseButton" type="TextureButton" parent="PuzzlePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
ignore_texture_size = true
stretch_mode = 1

[node name="CloseLabel" type="Label" parent="PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Zavrieť"
horizontal_alignment = 1
vertical_alignment = 1
