# Implementácia virtuálnej klávesnice

## Prehľad
Vytvorená bola kompletná virtuálna klávesnica pre hru "Prekliate dedičstvo", ktorá nahradí systémovú mobilnú klávesnicu v prvej hádanke (CaesarCipherPuzzle).

## Súbory

### 1. `scenes/VirtualKeyboard.tscn`
- Hlavná scéna virtuálnej klávesnice
- Obsahuje NinePatchRect s textúrou rámčeka klávesnice
- Štruktúra radov pre QWERTY rozloženie
- Optimalizované pre mobilné zariadenia (720x1280)

### 2. `scripts/VirtualKeyboard.gd`
- Hlavný skript pre virtuálnu klávesnicu
- Trieda `VirtualKeyboard` rozširuje `Control`
- Automatické vytvorenie klávesov podľa QWERTY rozloženia

## Funkcie

### Rozloženie klávesnice
```gdscript
var keyboard_layout = [
    ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", "Ô"],
    ["A", "S", "D", "F", "G", "H", "J", "K", "L", "Ľ"],
    ["Z", "X", "C", "V", "B", "N", "M", "Ž", "Š", "Č"],
    ["SPACE", "BACKSPACE", "ENTER"]
]
```

### Podporované znaky
- Všetky základné písmená A-Z
- Slovenské diakritické znaky: Ô, Ľ, Ž, Š, Č
- Špeciálne klávesy: SPACE, BACKSPACE, ENTER

### Vizuálne efekty
- Animácie pri stlačení klávesov (modulate efekt)
- Tieň pre lepšiu čitateľnosť textu
- Responzívne rozloženie pre mobilné zariadenia

### Zvukové efekty
- Integrácia s AudioManager
- Zvuk pri stlačení každej klávesy
- Konzistentnosť s ostatnými UI zvukmi

## Integrácia s CaesarCipherPuzzle

### Upravené súbory
1. `scripts/CaesarCipherPuzzle.gd` - pridané funkcie pre virtuálnu klávesnicu
2. `scenes/CaesarCipherPuzzle.tscn` - deaktivovaná systémová klávesnica

### Nové funkcie v CaesarCipherPuzzle
- `create_virtual_keyboard()` - vytvorenie a pripojenie klávesnice
- `_on_virtual_key_pressed()` - spracovanie stlačenia písmena
- `_on_virtual_backspace_pressed()` - spracovanie backspace
- `_on_virtual_space_pressed()` - spracovanie medzery
- `_on_virtual_enter_pressed()` - spracovanie enter
- `_on_input_focus_entered()` - zobrazenie klávesnice
- `_on_input_focus_exited()` - správa fokusu

## Použité assety
- `assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel4.png` - rámček klávesnice
- `assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png` - normálny stav klávesov
- `assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png` - hover/pressed stav klávesov

## Technické detaily

### Pozícia klávesnice
- Umiestnená v spodnej časti obrazovky
- Anchor: center bottom
- Veľkosť: 720x200 pixelov
- Automatické skrytie/zobrazenie

### Font a štýlovanie
- Použitý FontLoader.apply_ui_font() pre konzistentnosť
- Veľkosť fontu: 14px
- Tieň pre lepšiu čitateľnosť
- Centrované zarovnanie textu

### Signály
```gdscript
signal key_pressed(letter: String)
signal backspace_pressed
signal space_pressed  
signal enter_pressed
```

## Výhody implementácie

1. **Mobilná optimalizácia**: Žiadna systémová klávesnica na iOS
2. **Konzistentný dizajn**: Používa herné assety a fonty
3. **Slovenská podpora**: Diakritické znaky pre slovenčinu
4. **Modulárnosť**: Ľahko rozšíriteľné na ďalšie hádanky
5. **Vizuálne efekty**: Animácie a zvukové efekty
6. **Responzívnosť**: Optimalizované pre mobilné zariadenia

## Budúce rozšírenia

### Pre ďalšie hádanky
Klávesnicu možno ľahko integrovať do ďalších puzzle scén:

```gdscript
# V ľubovoľnej puzzle scéne
var virtual_keyboard: VirtualKeyboard

func _ready():
    create_virtual_keyboard()

func create_virtual_keyboard():
    var keyboard_scene = preload("res://scenes/VirtualKeyboard.tscn")
    virtual_keyboard = keyboard_scene.instantiate()
    add_child(virtual_keyboard)
    virtual_keyboard.set_target_input_field(input_field)
```

### Možné vylepšenia
1. Podpora pre čísla (0-9)
2. Prepínanie medzi veľkými/malými písmenami
3. Ďalšie slovenské znaky (Á, É, Í, Ó, Ú, Ý)
4. Rôzne témy klávesnice
5. Haptic feedback na mobilných zariadeniach

## Testovanie

### Test scéna
Vytvorená bola samostatná test scéna pre overenie funkčnosti:
- `scenes/VirtualKeyboardTest.tscn` - test scéna
- `scripts/VirtualKeyboardTest.gd` - test logika

### Spustenie testu
1. Otvorte projekt v Godot
2. Spustite scénu `VirtualKeyboardTest.tscn`
3. Kliknite do textového poľa
4. Testujte všetky funkcie klávesnice

### Opravené chyby
- **Parser Error**: Opravená chyba s `play_ui_sound()` - funkcia očakáva parameter `sound_path`
- **Zvukové efekty**: Používa `AudioManager.play_menu_button_sound()` pre konzistentnosť
- **Dvojité zadávanie**: Opravené duplicitné spracovanie klávesov - VirtualKeyboard teraz emituje len signály
- **Rámček klávesnice**: Zmenený z `klavesnica_ram.png` na `pozadie.png`

### Kompatibilita
- Testované v Godot 4.2+
- Optimalizované pre iOS export
- Kompatibilné s mobilnou orientáciou 720x1280
- Funkčné s existujúcim AudioManager systémom
