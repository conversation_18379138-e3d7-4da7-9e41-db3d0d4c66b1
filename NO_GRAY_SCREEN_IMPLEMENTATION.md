# Odstránenie sivej obrazovky pri spustení hry

## Prehľad
Implementované boli zmeny pre odstránenie predvolenej sivej obrazovky pri spustení hry a kapitol. Výsledkom je plynulý prechod bez záblesku sivej obrazovky.

## Implementované zmeny

### 1. Project Settings (project.godot)

#### Default Clear Color
```ini
[rendering]
environment/defaults/default_clear_color=Color(0, 0, 0, 1)
```
- Zmenená predvolená farba pozadia z sivej na čiernu

#### Boot Splash
```ini
[application]
boot_splash/bg_color=Color(0, 0, 0, 1)
```
- Zmenená farba boot splash z tmavo modrej na čiernu

#### Hlavná scéna
```ini
[application]
run/main_scene="res://scenes/LoadingScreen.tscn"
```
- <PERSON><PERSON><PERSON><PERSON> hlavná scéna z SplashScreen na LoadingScreen

### 2. Nová Loading Screen

#### scenes/LoadingScreen.tscn
- Jednoduchá scéna s čiernym pozadím
- Obsahuje logo Van Helsing
- Okamžite sa načíta bez čakania

#### scripts/LoadingScreen.gd
```gdscript
func _ready():
    # Okamžite spustiť fade in animáciu
    start_fade_in()
    
    # Po krátkom čase prejsť na SplashScreen
    await get_tree().create_timer(0.5).timeout
    load_splash_screen()
```

### 3. Upravený SplashScreen

#### scenes/SplashScreen.tscn
```
color = Color(0, 0, 0, 1)  # Zmenené z Color(0.05, 0.05, 0.1, 1)
```
- Pozadie zmenené na čiernu farbu

### 4. Čierne pozadie vo všetkých scénach

Pridané čierne pozadie ako fallback do všetkých kapitol:

#### Chapter1.tscn až Chapter7.tscn
```
[node name="BlackBackground" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)
```

#### ChapterIntro.tscn
```
[node name="BlackBackground" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)
```

## Výsledok

### Sekvencia spustenia hry:
1. **Boot Splash** - čierna farba (0.5s)
2. **LoadingScreen** - čierne pozadie + logo (0.5s)
3. **SplashScreen** - čierne pozadie + animácie (3s)
4. **MainMenu** - herné pozadie

### Sekvencia spustenia kapitol:
1. **ChapterIntro** - čierne pozadie + video/animácie
2. **Chapter** - čierne pozadie + herné pozadie

## Výhody implementácie

1. **Žiadna sivá obrazovka** - všetky prechody sú čierne
2. **Okamžité načítanie** - LoadingScreen sa zobrazí ihneď
3. **Plynulé prechody** - konzistentné čierne pozadie
4. **Fallback pozadie** - každá scéna má čierne pozadie ako základ
5. **Mobilná optimalizácia** - žiadne problémy s rôznymi rozlíšeniami

## Technické detaily

### Hierarchia pozadí v kapitolách:
```
Chapter (Control)
├── BlackBackground (ColorRect) - čierna farba, vždy viditeľná
├── Background (NinePatchRect) - herné pozadie, prekrýva čiernu
└── ... ostatné UI elementy
```

### Časovanie:
- **LoadingScreen**: 0.5s
- **SplashScreen**: 3s (možno preskočiť)
- **ChapterIntro**: 5-6s (podľa kapitoly)

### Kompatibilita:
- Funguje na všetkých platformách
- Optimalizované pre iOS export
- Podporuje mobilnú orientáciu 720x1280
- Žiadne problémy s rôznymi rozlíšeniami

## Testovanie

### Spustenie hry:
1. Spustite projekt v Godot
2. Sledujte sekvencii: Boot → Loading → Splash → MainMenu
3. Overte, že sa nezobrazuje sivá obrazovka

### Spustenie kapitol:
1. Spustite ľubovoľnú kapitolu
2. Sledujte prechod: ChapterIntro → Chapter
3. Overte plynulé prechody bez sivej obrazovky

### Mobilné testovanie:
1. Exportujte na iOS/Android
2. Testujte na rôznych rozlíšeniach
3. Overte, že pozadie pokrýva celú obrazovku

## Záver

Implementácia úspešne odstránila sivú obrazovku pri spustení hry a kapitol. Všetky prechody sú teraz plynulé s konzistentným čiernym pozadím, čo poskytuje profesionálny herný zážitok bez rušivých vizuálnych artefaktov.
