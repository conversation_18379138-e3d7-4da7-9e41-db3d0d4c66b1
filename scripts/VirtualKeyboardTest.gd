extends Control

@onready var test_input: LineEdit = $VBoxContainer/TestInput
@onready var show_button: Button = $VBoxContainer/ButtonContainer/ShowKeyboardButton
@onready var hide_button: Button = $VBoxContainer/ButtonContainer/HideKeyboardButton
@onready var back_button: Button = $VBoxContainer/BackButton
@onready var virtual_keyboard: VirtualKeyboard = $VirtualKeyboard
@onready var title_label: Label = $VBoxContainer/Title

func _ready():
	print("🧪 VirtualKeyboardTest: Inicializácia test scény")
	
	# Pripojenie signálov
	show_button.pressed.connect(_on_show_keyboard_pressed)
	hide_button.pressed.connect(_on_hide_keyboard_pressed)
	back_button.pressed.connect(_on_back_pressed)
	test_input.focus_entered.connect(_on_input_focus_entered)
	test_input.focus_exited.connect(_on_input_focus_exited)
	
	# Aplikovanie fontov
	FontLoader.apply_chapter_title_font(title_label)
	FontLoader.apply_ui_font(show_button)
	FontLoader.apply_ui_font(hide_button)
	FontLoader.apply_ui_font(back_button)
	FontLoader.apply_character_dialogue_font(test_input)
	
	# Pripojenie klávesnice k test input
	if virtual_keyboard:
		virtual_keyboard.set_target_input_field(test_input)
		virtual_keyboard.key_pressed.connect(_on_key_pressed)
		virtual_keyboard.backspace_pressed.connect(_on_backspace_pressed)
		virtual_keyboard.space_pressed.connect(_on_space_pressed)
		virtual_keyboard.enter_pressed.connect(_on_enter_pressed)
	
	print("🧪 VirtualKeyboardTest: Inicializácia dokončená")

func _on_show_keyboard_pressed():
	"""Zobrazí virtuálnu klávesnicu"""
	print("🧪 Zobrazujem klávesnicu")
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(test_input)
	
	# Prehrať UI zvuk
	if AudioManager:
		AudioManager.play_menu_button_sound()

func _on_hide_keyboard_pressed():
	"""Skryje virtuálnu klávesnicu"""
	print("🧪 Skrývam klávesnicu")
	if virtual_keyboard:
		virtual_keyboard.hide_keyboard()
	
	# Prehrať UI zvuk
	if AudioManager:
		AudioManager.play_menu_button_sound()

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	print("🧪 Návrat do hlavného menu")
	
	# Prehrať UI zvuk
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	# Skryť klávesnicu pred odchodom
	if virtual_keyboard:
		virtual_keyboard.hide_keyboard()
	
	# Návrat do hlavného menu
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_input_focus_entered():
	"""Automaticky zobrazí klávesnicu pri aktivácii input field"""
	print("🧪 Input field aktivovaný - zobrazujem klávesnicu")
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(test_input)

func _on_input_focus_exited():
	"""Input field deaktivovaný"""
	print("🧪 Input field deaktivovaný")
	# Neskrývame klávesnicu automaticky

# Signály z klávesnice pre debugging
func _on_key_pressed(letter: String):
	"""Debug výpis pri stlačení písmena"""
	print("🧪 Stlačené písmeno: ", letter)

func _on_backspace_pressed():
	"""Debug výpis pri stlačení backspace"""
	print("🧪 Stlačený backspace")

func _on_space_pressed():
	"""Debug výpis pri stlačení medzery"""
	print("🧪 Stlačená medzera")

func _on_enter_pressed():
	"""Debug výpis pri stlačení enter"""
	print("🧪 Stlačený enter")
	print("🧪 Aktuálny text: '", test_input.text, "'")

func _input(event):
	"""Umožní ukončenie testu klávesou Escape"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
