extends Control
class_name VampireArithmeticPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var equations_label: RichTextLabel = $PuzzlePanel/VBoxContainer/EquationsLabel
@onready var question_label: Label = $PuzzlePanel/VBoxContainer/QuestionLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: Button = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var virtual_keyboard: VirtualKeyboard

var correct_answer: int = 26  # 🦇(8) + 🩸(11) + ⚰(7) = 26
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	# Vytvorenie virtuálnej klávesnice
	create_virtual_keyboard()

	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if answer_field:
		answer_field.text_submitted.connect(_on_answer_submitted)
		answer_field.focus_entered.connect(_on_input_focus_entered)
		answer_field.focus_exited.connect(_on_input_focus_exited)

func show_puzzle():
	show()
	reset_puzzle()
	# Uložiť stav puzzle
	GameManager.set_game_state_puzzle("VampireArithmeticPuzzle")
	if answer_field:
		answer_field.grab_focus()
	# Zobraziť virtuálnu klávesnicu
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(answer_field)

func reset_puzzle():
	hint_level = 0
	if answer_field:
		answer_field.text = ""
		answer_field.placeholder_text = "Zadajte 2-ciferné číslo..."

func _on_submit_pressed():
	check_answer()

func _on_answer_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field:
		return
	
	var player_answer = answer_field.text.strip_edges()
	
	if player_answer.is_empty():
		show_feedback("Zadajte odpoveď!", false)
		return
	
	if not player_answer.is_valid_int():
		show_feedback("Zadajte platné číslo!", false)
		return
	
	var answer_int = player_answer.to_int()
	
	if answer_int == correct_answer:
		AudioManager.play_puzzle_success_sound()
		show_feedback("Výborne! Elixír je pripravený!", true)
		await get_tree().create_timer(1.5).timeout
		puzzle_solved.emit()
		hide()
	else:
		AudioManager.play_puzzle_error_sound()
		show_feedback("Nesprávne. Skúste znova.", false)
		if answer_field:
			answer_field.text = ""

func show_feedback(message: String, success: bool):
	if answer_field:
		if success:
			answer_field.modulate = Color.GREEN
		else:
			answer_field.modulate = Color.RED
		
		# Zobraz správu v placeholder
		answer_field.placeholder_text = message
		
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 1.0)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Každý symbol skrýva číslo. Začnite s netopierom."
		2:
			return "Ak dva netopiere dávajú 16, koľko je jeden netopier?"
		3:
			return "Netopier=8, Krv=11, Rakva=7. Spočítajte ich."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()

	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func create_virtual_keyboard():
	"""Vytvorí a pripojí virtuálnu klávesnicu"""
	print("🔤 VampireArithmeticPuzzle: Vytváram virtuálnu klávesnicu...")

	var keyboard_scene = preload("res://scenes/VirtualKeyboard.tscn")
	virtual_keyboard = keyboard_scene.instantiate()
	add_child(virtual_keyboard)

	# Pripojenie signálov klávesnice
	virtual_keyboard.key_pressed.connect(_on_virtual_key_pressed)
	virtual_keyboard.backspace_pressed.connect(_on_virtual_backspace_pressed)
	virtual_keyboard.space_pressed.connect(_on_virtual_space_pressed)
	virtual_keyboard.enter_pressed.connect(_on_virtual_enter_pressed)

	print("🔤 VampireArithmeticPuzzle: Virtuálna klávesnica vytvorená a pripojená")

func _on_virtual_key_pressed(letter: String):
	"""Spracuje stlačenie písmena na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += letter

func _on_virtual_backspace_pressed():
	"""Spracuje stlačenie backspace na virtuálnej klávesnici"""
	if answer_field and answer_field.text.length() > 0:
		answer_field.text = answer_field.text.substr(0, answer_field.text.length() - 1)

func _on_virtual_space_pressed():
	"""Spracuje stlačenie medzery na virtuálnej klávesnici"""
	if answer_field:
		answer_field.text += " "

func _on_virtual_enter_pressed():
	"""Spracuje stlačenie enter na virtuálnej klávesnici"""
	check_answer()

func _on_input_focus_entered():
	"""Zobrazí virtuálnu klávesnicu keď sa aktivuje textové pole"""
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(answer_field)

func _on_input_focus_exited():
	"""Skryje virtuálnu klávesnicu keď sa deaktivuje textové pole"""
	# Neskrývame klávesnicu automaticky, nech zostane zobrazená

func _on_close_pressed():
	# Skryť virtuálnu klávesnicu
	if virtual_keyboard:
		virtual_keyboard.hide_keyboard()

	# Obnoviť story stav
	GameManager.set_game_state_story()
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
