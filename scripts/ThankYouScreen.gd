extends Control

# Záverečná ďakovná obrazovka po ukončení hry

@onready var background: TextureRect = $Background
@onready var main_panel: NinePatchRect = $MainPanel
@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var scroll_container: ScrollContainer = $MainPanel/ContentContainer/ContentPanel/ScrollContainer
@onready var thank_you_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/ThankYouLabel
@onready var game_completed_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameCompletedText
@onready var credits_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsText
@onready var main_menu_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton
@onready var main_menu_label: Label = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton/MainMenuLabel
@onready var fade_overlay: ColorRect = $FadeOverlay

# Automatický timer pre návrat do menu
var auto_return_timer: Timer

func _ready():
	print("🎉 DEBUG: Ďakovná obrazovka _ready() začína")

	# Krátky fade in na začiatku
	_fade_in_screen()

	# Pripojenie signálov
	setup_signals()
	print("🎉 DEBUG: Signály pripojené")

	# Aplikovanie fontov a štýlov
	apply_styling()
	print("🎉 DEBUG: Štýly aplikované")

	# Nastavenie obsahu
	setup_content()
	print("🎉 DEBUG: Obsah nastavený")
	
	# Nastavenie fokusu
	if main_menu_button:
		main_menu_button.grab_focus()
		print("🎉 DEBUG: Fokus nastavený na button")
	
	# Vytvorenie automatického timera (30 sekúnd)
	setup_auto_return_timer()

	# Nastavenie mobilného scrollovania
	setup_mobile_scrolling()

	print("🎉 DEBUG: Ďakovná obrazovka úspešne načítaná a zobrazená!")

	# Ponechať hudbu z kapitoly 7 (epilógu)
	print("🎉 DEBUG: Ponechávam hudbu z epilógu")

func setup_signals():
	"""Pripojí signály pre buttony"""
	if main_menu_button:
		main_menu_button.pressed.connect(_on_main_menu_pressed)

func setup_auto_return_timer():
	"""Vytvorí timer pre automatický návrat do menu po 30 sekundách"""
	auto_return_timer = Timer.new()
	auto_return_timer.wait_time = 30.0
	auto_return_timer.one_shot = true
	auto_return_timer.timeout.connect(_on_auto_return_timeout)
	add_child(auto_return_timer)
	auto_return_timer.start()
	print("🎉 DEBUG: Automatický timer nastavený na 30 sekúnd")

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""
	# Aplikovanie fontov cez FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 28)
		
		if thank_you_label:
			FontLoader.apply_font_style(thank_you_label, "chapter_title")
			thank_you_label.add_theme_color_override("font_color", Color("#D4AF37"))
			thank_you_label.add_theme_font_size_override("font_size", 24)
		
		if game_completed_text:
			FontLoader.apply_font_style(game_completed_text, "dialogue")
			game_completed_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			game_completed_text.add_theme_font_size_override("normal_font_size", 18)
		
		if credits_text:
			FontLoader.apply_font_style(credits_text, "dialogue")
			credits_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			credits_text.add_theme_font_size_override("normal_font_size", 16)
		
		if main_menu_label:
			FontLoader.apply_font_style(main_menu_label, "ui")
			main_menu_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			main_menu_label.add_theme_font_size_override("font_size", 18)

func setup_content():
	"""Nastaví obsah ďakovnej obrazovky"""
	if title_label:
		title_label.text = "VAN HELSING: PREKLIATE DEDIČSTVO"

	if thank_you_label:
		thank_you_label.text = "ĎAKUJEME ZA HRANIE!"

	if game_completed_text:
		game_completed_text.text = """[center][b]Gratulujeme![/b][/center]

[center]Úspešne ste dokončili všetky kapitoly[/center]
[center]dobrodružnej hry Van Helsing: Prekliate dedičstvo.[/center]

[center]Všetky záhady boli odhalené,[/center]
[center]prekliatie je zlomené a hrad je váš.[/center]

[center]Dúfame, že ste si užili toto dobrodružstvo[/center]
[center]plné tajomstiev a záhad.[/center]"""

	if credits_text:
		credits_text.text = """[center][b]Tvorca hry[/b][/center]
[center]Vladimír Seman[/center]
[center]0940 400 222[/center]
[center]<EMAIL>[/center]

[center][b]Špeciálne poďakovanie[/b][/center]
[center]Escape Land Žilina[/center]
[center]www.escapeland.sk[/center]

[center][i]Automatický návrat do hlavného menu za 30 sekúnd[/i][/center]"""

	if main_menu_label:
		main_menu_label.text = "HLAVNÉ MENU"

func _on_main_menu_pressed():
	"""Návrat do hlavného menu"""
	print("🎉 DEBUG: _on_main_menu_pressed() volaná - používateľ klikol na button")
	_stop_timer_and_return_to_menu()

func _on_auto_return_timeout():
	"""Automatický návrat do menu po 30 sekundách"""
	print("🎉 DEBUG: Automatický návrat do menu po 30 sekundách")
	_stop_timer_and_return_to_menu()

func _stop_timer_and_return_to_menu():
	"""Zastaví timer a vráti sa do hlavného menu s fade out"""
	if auto_return_timer and auto_return_timer.is_inside_tree():
		auto_return_timer.stop()

	AudioManager.play_menu_button_sound()
	await _fade_out_screen()
	GameManager.go_to_main_menu()

func setup_mobile_scrolling():
	"""Nastaví optimálne scrollovanie pre mobilné zariadenia"""
	if scroll_container:
		# Povoliť vertikálne scrollovanie s automatickým scrollbarom
		scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
		scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO

		# Nastaviť deadzone pre lepšie dotyky
		scroll_container.scroll_deadzone = 0

		# Povoliť smooth scrolling
		scroll_container.follow_focus = true

		print("📱 Mobilné scrollovanie nastavené pre ThankYouScreen")

func _input(event):
	"""Spracovanie klávesových skratiek a mobilných dotykových gestúr"""
	if event.is_action_pressed("ui_accept") or event.is_action_pressed("ui_cancel"):
		print("🎉 DEBUG: _input() zachytil klávesovú skratku - automatický návrat do menu")
		_stop_timer_and_return_to_menu()

	# Podpora pre mobilné scrollovanie pomocou swipe gestúr
	if event is InputEventScreenDrag and scroll_container:
		var drag_speed = 2.0
		scroll_container.scroll_vertical -= int(event.relative.y * drag_speed)

func _fade_in_screen():
	"""Krátky fade in na začiatku obrazovky"""
	if fade_overlay:
		fade_overlay.color.a = 1.0
		var tween = create_tween()
		tween.tween_property(fade_overlay, "color:a", 0.0, 0.3)

func _fade_out_screen():
	"""Krátky fade out pred odchodom z obrazovky"""
	if fade_overlay:
		var tween = create_tween()
		tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
		await tween.finished
