# Implement<PERSON>cia krátkich fade efektov

## Prehľad
Pridané boli krátke fade efekty (0.2-0.3s) pred a po scénach pre ešte plynulej<PERSON>ie prechody bez sivej obrazovky.

## Implementované fade efekty

### 1. LoadingScreen
**Súbory:** `scripts/LoadingScreen.gd`, `scenes/LoadingScreen.tscn`

#### Fade In (0.2s)
```gdscript
func start_fade_in():
    var tween = create_tween()
    tween.set_parallel(true)
    tween.tween_property(fade_overlay, "color:a", 0.0, 0.2)
    tween.tween_property(logo, "modulate:a", 1.0, 0.3)
```

#### Fade Out (0.2s)
```gdscript
func fade_out():
    var tween = create_tween()
    tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
    await tween.finished
```

### 2. SplashScreen
**Súbor:** `scripts/SplashScreen.gd`

#### Fade In (0.3s)
```gdscript
# Krátky fade in z čiernej (0.3s)
var fade_tween = create_tween()
fade_tween.tween_property(fade_overlay, "color:a", 0.0, 0.3)
```

#### Fade Out (0.3s)
```gdscript
# Krátky fade out animácia (0.3s)
fade_tween.tween_property(fade_overlay, "color:a", 1.0, 0.3)
```

### 3. ChapterIntro
**Súbory:** `scripts/ChapterIntro.gd`, `scenes/ChapterIntro.tscn`

#### Fade Out pred prechodom na kapitolu (0.2s)
```gdscript
func _fade_out_to_chapter():
    fade_overlay.visible = true
    var tween = create_tween()
    tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
    await tween.finished
```

### 4. Chapter (všetky kapitoly)
**Súbor:** `scripts/Chapter.gd`

#### Fade In na začiatku kapitoly (0.3s)
```gdscript
func _fade_in_chapter():
    var fade_overlay = get_node_or_null("BlackBackground")
    if fade_overlay:
        fade_overlay.modulate.a = 1.0
        var tween = create_tween()
        tween.tween_property(fade_overlay, "modulate:a", 0.0, 0.3)
```

#### Fade Out pred prechodom na ďalšiu kapitolu (0.2s)
```gdscript
func _fade_out_chapter():
    var fade_overlay = get_node_or_null("BlackBackground")
    if fade_overlay:
        var tween = create_tween()
        tween.tween_property(fade_overlay, "modulate:a", 1.0, 0.2)
        await tween.finished
```

### 5. ThankYouScreen
**Súbory:** `scripts/ThankYouScreen.gd`, `scenes/ThankYouScreen.tscn`

#### Fade In na začiatku (0.3s)
```gdscript
func _fade_in_screen():
    if fade_overlay:
        fade_overlay.color.a = 1.0
        var tween = create_tween()
        tween.tween_property(fade_overlay, "color:a", 0.0, 0.3)
```

#### Fade Out pred návratom do menu (0.2s)
```gdscript
func _fade_out_screen():
    if fade_overlay:
        var tween = create_tween()
        tween.tween_property(fade_overlay, "color:a", 1.0, 0.2)
        await tween.finished
```

## Sekvencia fade efektov

### Spustenie hry:
```
Boot Splash → LoadingScreen (fade in 0.2s) → (fade out 0.2s) → 
SplashScreen (fade in 0.3s) → (fade out 0.3s) → MainMenu
```

### Spustenie kapitoly:
```
ChapterIntro → (fade out 0.2s) → Chapter (fade in 0.3s)
```

### Prechod medzi kapitolami:
```
Chapter (fade out 0.2s) → ChapterIntro → (fade out 0.2s) → 
Next Chapter (fade in 0.3s)
```

### Koniec hry:
```
Chapter 7 (fade out 0.2s) → ThankYouScreen (fade in 0.3s) → 
(fade out 0.2s) → MainMenu
```

## Technické detaily

### Použité overlay elementy:
- **LoadingScreen**: `FadeOverlay` (ColorRect)
- **SplashScreen**: `FadeOverlay` (ColorRect) - už existoval
- **ChapterIntro**: `FadeOverlay` (ColorRect) - už existoval
- **Chapter**: `BlackBackground` (ColorRect) - použitý ako fade overlay
- **ThankYouScreen**: `FadeOverlay` (ColorRect) - nový

### Časovanie:
- **Fade In**: 0.2-0.3s (rýchle zobrazenie)
- **Fade Out**: 0.2s (rýchle skrytie)
- **Celkový čas pridaný**: ~0.4-0.5s na prechod

### Farby:
- Všetky fade overlay používajú čiernu farbu `Color(0, 0, 0, 1)`
- Konzistentné s celkovým dizajnom hry

## Výhody implementácie

1. **Plynulejšie prechody** - žiadne "skoky" medzi scénami
2. **Profesionálny vzhľad** - smooth fade efekty
3. **Krátke časovanie** - nepredlžuje hru výrazne
4. **Konzistentnosť** - rovnaké efekty vo všetkých scénach
5. **Mobilná optimalizácia** - funguje na všetkých zariadeniach

## Testovanie

### Spustenie hry:
1. Spustite projekt v Godot
2. Sledujte plynulé fade prechody
3. Overte, že sa nezobrazuje sivá obrazovka

### Prechody medzi kapitolami:
1. Dokončite kapitolu
2. Sledujte fade out → fade in sekvencii
3. Overte plynulý prechod bez záblesku

### Mobilné testovanie:
1. Exportujte na iOS/Android
2. Testujte na rôznych zariadeniach
3. Overte, že fade efekty fungují správne

## Záver

Implementácia krátkich fade efektov výrazne zlepšila vizuálny dojem hry. Všetky prechody sú teraz plynulé a profesionálne, bez rušivých vizuálnych artefaktov. Časovanie je optimalizované pre rýchly, ale elegantný prechod medzi scénami.
